import { useState, useEffect } from "react";
import { WidgetsProvider, WidgetsCatalog, Layout } from "./lib";
import type { Widget } from "./lib";
import type { Layout as RGLLayout } from "react-grid-layout";
import { CounterWidget } from "./example-widgets/CounterWidget";
import { GreetingWidget } from "./example-widgets/GreetingWidget";
import { TodoWidget } from "./example-widgets/TodoWidget";
import "./App.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";

// Widget registry for restoring components from localStorage
const widgetRegistry = {
  counter: CounterWidget,
  greeting: GreetingWidget,
  todo: TodoWidget,
};

// Example widgets configuration
const exampleWidgets: Widget[] = [
  {
    id: "counter",
    name: "Counter Widget",
    description: "A simple counter that can increment and decrement values",
    component: CounterWidget,
    category: "Interactive",
    tags: ["counter", "interactive", "demo"],
    props: { initialValue: 0, step: 1 },
    layout: { w: 3, h: 4, x: 0, y: 0 },
  },
  {
    id: "greeting",
    name: "Greeting Widget",
    description: "A personalized greeting widget with name input",
    component: GreetingWidget,
    category: "Interactive",
    tags: ["greeting", "input", "demo"],
    props: { defaultName: "Developer" },
    layout: { w: 4, h: 3, x: 3, y: 0 },
  },
  {
    id: "todo",
    name: "Todo List Widget",
    description: "A simple todo list to manage tasks",
    component: TodoWidget,
    category: "Productivity",
    tags: ["todo", "list", "productivity", "tasks"],
    layout: { w: 5, h: 6, x: 7, y: 0 },
  },
];

// Helper function to get component from registry
const getWidgetComponent = (widgetId: string) => {
  return widgetRegistry[widgetId as keyof typeof widgetRegistry];
};

// Helper function to save layout to localStorage
const saveLayout = (layout: RGLLayout[], widgets: Widget[]) => {
  localStorage.setItem('widget-layout', JSON.stringify(layout));
  // Save widgets without components (can't serialize functions)
  const serializableWidgets = widgets.map((widget) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { component, ...serializableWidget } = widget;
    return serializableWidget;
  });
  localStorage.setItem('widget-dropped', JSON.stringify(serializableWidgets));
};

// Helper function to load layout from localStorage
const loadLayout = (): { layout: RGLLayout[], widgets: Widget[] } => {
  try {
    const savedLayout = localStorage.getItem('widget-layout');
    const savedWidgets = localStorage.getItem('widget-dropped');

    if (savedLayout && savedWidgets) {
      const layout = JSON.parse(savedLayout);
      const widgets = JSON.parse(savedWidgets);

      // Restore widgets with components from registry
      const restoredWidgets = widgets
        .map((widget: Omit<Widget, 'component'> & { id: string }) => {
          const baseId = widget.id.split('-')[0];
          const component = widgetRegistry[baseId as keyof typeof widgetRegistry];
          if (component !== undefined) {
            return { ...widget, component };
          }
          return null;
        })
        .filter((widget: Widget | null): widget is Widget => widget !== null);

      return { layout, widgets: restoredWidgets };
    }
  } catch (error) {
    console.warn('Failed to load layout:', error);
  }
  return { layout: [], widgets: [] };
};

function App() {
  const [droppedWidgets, setDroppedWidgets] = useState<Widget[]>([]);
  const [currentLayout, setCurrentLayout] = useState<RGLLayout[]>([]);

  // Load saved layout on mount
  useEffect(() => {
    const { layout, widgets } = loadLayout();
    setCurrentLayout(layout);
    setDroppedWidgets(widgets);
  }, []);


  // Handle widget drop from catalog to layout
  const handleDrop = (_layout: RGLLayout[], item: RGLLayout, e: Event) => {
    const dragEvent = e as DragEvent;
    const dragData = dragEvent.dataTransfer?.getData("application/json");

    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        if (data.widget) {
          const component = getWidgetComponent(data.widget.id);

          if (component !== undefined) {
            const newWidget = {
              ...data.widget,
              component,
              id: `${data.widget.id}-${Date.now()}`, // Unique ID for each instance
              layout: {
                ...data.widget.layout,
                x: item.x,
                y: item.y,
                w: Math.max(item.w, 4), // Minimum size
                h: Math.max(item.h, 3),
              },
            };

            setDroppedWidgets((prev) => [...prev, newWidget]);
          } else {
            console.error("Widget component not found:", data.widget.id);
          }
        }
      } catch (error) {
        console.error("Error parsing drag data:", error);
      }
    }
  };

  // Handle layout changes (resize/move)
  const handleLayoutChange = (layout: RGLLayout[]) => {
    setCurrentLayout(layout);

    // Update widget layout properties
    const updatedWidgets = droppedWidgets.map((widget) => {
      const layoutItem = layout.find((item) => item.i === widget.id);
      if (layoutItem) {
        return {
          ...widget,
          layout: {
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return widget;
    });

    setDroppedWidgets(updatedWidgets);
    saveLayout(layout, updatedWidgets);
  };

  // Clear all widgets and layout
  const clearLayout = () => {
    localStorage.removeItem('widget-layout');
    localStorage.removeItem('widget-dropped');
    setDroppedWidgets([]);
    setCurrentLayout([]);
  };

  // Remove a widget from the layout
  const removeWidget = (widgetId: string) => {
    const newDroppedWidgets = droppedWidgets.filter((w) => w.id !== widgetId);
    const newLayout = currentLayout.filter((l) => l.i !== widgetId);

    setDroppedWidgets(newDroppedWidgets);
    setCurrentLayout(newLayout);
    saveLayout(newLayout, newDroppedWidgets);
  };

  // Render dropped widgets in the layout
  const renderDroppedWidgets = () => {
    return droppedWidgets.map((widget) => {
      const WidgetComponent = widget.component;
      const currentLayoutItem = currentLayout.find((item) => item.i === widget.id);
      const gridData = currentLayoutItem || widget.layout;

      return (
        <div key={widget.id} data-grid={gridData} className="grid-widget-container">
          <div className="grid-widget-content">
            <div className="widget-actions">
              <button
                className="widget-action-btn"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  removeWidget(widget.id);
                }}
                onMouseDown={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                title="Remove widget"
                aria-label="Remove widget"
              >
                ✕
              </button>
            </div>
            <div
              className="widget-body"
              onMouseDown={(e) => {
                // Prevent dragging when clicking on interactive elements
                const target = e.target as HTMLElement;
                if (
                  target.tagName === "BUTTON" ||
                  target.tagName === "INPUT" ||
                  target.tagName === "SELECT" ||
                  target.tagName === "TEXTAREA"
                ) {
                  e.stopPropagation();
                }
              }}
            >
              <WidgetComponent
                key={`widget-${widget.id}`}
                {...(widget.props || {})}
                widgetId={`${widget.id}-instance`}
                enablePersistence={true}
              />
            </div>
          </div>
        </div>
      );
    });
  };

  return (
    <WidgetsProvider initialWidgets={exampleWidgets}>
      <div className="app-container">
        <header className="app-header">
          <h1 className="app-title">🚀 Tech4Fab Widget Package</h1>
          <p className="app-description">
            Interactive demonstration of WidgetsProvider, WidgetsCatalog, and
            Layout components with drag & drop functionality
          </p>
          <div className="header-actions">
            <button
              onClick={clearLayout}
              className="clear-button"
            >
              🗑️ Clear Layout & State
            </button>
          </div>
        </header>

        <div className="main-layout">
          {/* Widget Catalog */}
          <div className="catalog-container">
            <div className="catalog-header">
              <h3 className="catalog-title">🧩 Widget Catalog</h3>
              <p className="catalog-subtitle">
                Drag widgets to the layout area to add them →
              </p>
            </div>
            <WidgetsCatalog
              widgets={exampleWidgets}
              onDrag={(widget) => console.log('Dragging widget:', widget.name)}
              enableDrag={true}
            />
          </div>

          {/* Layout Area */}
          <div className="layout-area">
            <div className="layout-content">
              <div className="layout-container">
                <Layout
                  onDrop={handleDrop}
                  onLayoutChange={handleLayoutChange}
                  layout={currentLayout}
                  cols={12}
                  rowHeight={50}
                  margin={[16, 16]}
                  containerPadding={[24, 24]}
                  isDraggable={true}
                  isResizable={true}
                  isDroppable={true}
                >
                  {droppedWidgets.length === 0 ? (
                    <div className="empty-state">
                      <div className="empty-state-icon">🧩</div>
                      <div>Drop widgets here to get started!</div>
                    </div>
                  ) : (
                    renderDroppedWidgets()
                  )}
                </Layout>
              </div>
            </div>
          </div>
        </div>
      </div>
    </WidgetsProvider>
  );
}

export default App;
