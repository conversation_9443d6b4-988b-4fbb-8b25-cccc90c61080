import React from 'react';
import { useWidgets } from './WidgetsProvider';
import type { Widget } from './types';

interface WidgetsCatalogProps {
  className?: string;
  widgets?: Widget[];
  onWidgetSelect?: (widget: Widget) => void;
  onDrag?: (widget: Widget) => void;
  renderWidget?: (widget: Widget) => React.ReactNode;
  enableDrag?: boolean;
}

export const WidgetsCatalog: React.FC<WidgetsCatalogProps> = ({
  className = '',
  widgets: propWidgets,
  onWidgetSelect,
  onDrag,
  renderWidget,
  enableDrag = false,
}) => {
  const { widgets: contextWidgets } = useWidgets();
  const widgets = propWidgets || contextWidgets;

  console.log('WidgetsCatalog - propWidgets:', propWidgets);
  console.log('WidgetsCatalog - contextWidgets:', contextWidgets);
  console.log('WidgetsCatalog - final widgets:', widgets);
  console.log('WidgetsCatalog - enableDrag:', enableDrag);

  const handleWidgetClick = (widget: Widget) => {
    if (onWidgetSelect) {
      onWidgetSelect(widget);
    }
  };

  const handleDragStart = (widget: Widget, event: React.DragEvent) => {
    if (enableDrag) {
      event.dataTransfer.setData('application/json', JSON.stringify({
        widget,
        type: 'widget'
      }));
      event.dataTransfer.effectAllowed = 'copy';

      if (onDrag) {
        onDrag(widget);
      }
    }
  };

  const defaultRenderWidget = (widget: Widget) => {
    console.log('Rendering widget:', widget.name, 'draggable:', enableDrag);
    return (
      <div
        key={widget.id}
        className={`widget-card ${enableDrag ? 'draggable' : ''}`}
        draggable={enableDrag}
        onClick={() => handleWidgetClick(widget)}
        onDragStart={(e) => handleDragStart(widget, e)}
        style={{ border: '2px solid red' }} // Temporary debug styling
      >
        <h3>{widget.name}</h3>
        {widget.description && <p>{widget.description}</p>}
        {widget.tags && widget.tags.length > 0 && (
          <div className="widget-tags">
            {widget.tags.map((tag, index) => (
              <span key={index} className="widget-tag">
                {tag}
              </span>
            ))}
          </div>
        )}
        <div style={{ fontSize: '12px', color: 'blue' }}>
          Draggable: {enableDrag ? 'YES' : 'NO'}
        </div>
      </div>
    );
  };

  return (
    <div className={`widgets-catalog ${className}`}>
      <div className="widgets-grid">
        {widgets.length > 0 ? (
          widgets.map((widget) =>
            renderWidget ? renderWidget(widget) : defaultRenderWidget(widget)
          )
        ) : (
          <div className="no-widgets">No widgets found</div>
        )}
      </div>
    </div>
  );
};

export default WidgetsCatalog;
