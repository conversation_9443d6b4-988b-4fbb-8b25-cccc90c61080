import React from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import type { Layout as RGLLayout } from 'react-grid-layout';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface LayoutProps {
  children?: React.ReactNode;
  className?: string;
  onDrop?: (layout: RGLLayout[], layoutItem: RGLLayout, event: Event) => void;
  onLayoutChange?: (layout: RGLLayout[]) => void;
  layout?: RGLLayout[];
  cols?: number;
  rowHeight?: number;
  margin?: [number, number];
  containerPadding?: [number, number];
  isDraggable?: boolean;
  isResizable?: boolean;
  isDroppable?: boolean;
  width?: number;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  className = '',
  onDrop,
  onLayoutChange,
  layout = [],
  cols = 12,
  rowHeight = 30,
  margin = [10, 10],
  containerPadding = [10, 10],
  isDraggable = true,
  isResizable = true,
  isDroppable = true,
  width = 1200,
}) => {
  const droppingItem = {
    i: '__dropping-elem__',
    w: 4,
    h: 5,
  };

  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
  const colsByBreakpoint = { lg: cols, md: 10, sm: 6, xs: 4, xxs: 2 };

  return (
    <div className={`layout-grid ${className}`}>
      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: layout }}
        breakpoints={breakpoints}
        cols={colsByBreakpoint}
        rowHeight={rowHeight}
        margin={margin}
        containerPadding={containerPadding}
        isDraggable={isDraggable}
        isResizable={isResizable}
        isDroppable={isDroppable}
        onLayoutChange={onLayoutChange}
        onDrop={onDrop}
        droppingItem={droppingItem}
        width={width}
      >
        {children}
      </ResponsiveGridLayout>
    </div>
  );
};
